#!/usr/bin/env python3
"""
PHASE 2: ADVANCED FEATURE ENGINEERING + MODEL DIVERSITY
Building on Phase 1 (0.974898) with:
- Advanced feature engineering (behavioral patterns, ratio features)
- Model diversity (ExtraTreesClassifier, MLPClassifier, SVM)
- Target-aware feature selection

Expected improvements:
- Advanced features: +0.001 to +0.003 accuracy
- Model diversity: +0.001 to +0.002 accuracy
Total expected: +0.002 to +0.005 accuracy boost (target: 0.977+)
"""

import numpy as np
import pandas as pd
import warnings
import logging
from typing import Tuple, Dict

# Core ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.impute import KNNImputer
from sklearn.ensemble import StackingClassifier, RandomForestClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.feature_selection import SelectKBest, mutual_info_classif, VarianceThreshold

# Advanced models
from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase2PersonalityPredictor:
    """
    Phase 2: Advanced feature engineering + model diversity
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.feature_names = []
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.best_threshold = 0.5
        self.cv_scores = {}
        
        np.random.seed(random_state)
        
        print("=== PHASE 2: ADVANCED FEATURES + MODEL DIVERSITY ===")
        print("Building on Phase 1 (0.974898)")
        print("Target: +0.002 to +0.005 accuracy improvement → 0.977+")
        print("=" * 55)
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load and merge training and test data with personality dataset."""
        logger.info("Loading data files...")
        
        train_df = pd.read_csv("train.csv")
        test_df = pd.read_csv("test.csv")
        
        try:
            dataset_df = pd.read_csv("personality_dataset.csv")
        except FileNotFoundError:
            dataset_df = pd.read_csv("personality_datasert.csv")
        
        logger.info(f"Train shape: {train_df.shape}, Test shape: {test_df.shape}")
        
        # Prepare dataset for merging
        dataset_df = (
            dataset_df
            .rename(columns={'Personality': 'match_p'})
            .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                              'Going_outside', 'Drained_after_socializing', 
                              'Friends_circle_size', 'Post_frequency'])
        )
        
        # Merge columns
        merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency']
        
        # Merge datasets
        train_df = train_df.merge(dataset_df, how='left', on=merge_cols)
        test_df = test_df.merge(dataset_df, how='left', on=merge_cols)
        
        logger.info("Data loading and merging completed successfully")
        return train_df, test_df, dataset_df
    
    def create_behavioral_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        PHASE 2: Create behavioral pattern features based on personality psychology.
        """
        logger.info("Creating behavioral pattern features...")
        
        df = df.copy()
        
        # Social anxiety indicator
        df['social_anxiety_indicator'] = (
            (df['Stage_fear'] == 'Yes') & 
            (df['Social_event_attendance'] < df['Social_event_attendance'].median())
        ).astype(int)
        
        # Extroversion tendency score
        df['extroversion_tendency'] = (
            df['Social_event_attendance'] + 
            df['Going_outside'] + 
            df['Friends_circle_size'] + 
            df['Post_frequency'] - 
            df['Time_spent_Alone']
        ) / 5
        
        # Social energy balance
        df['social_energy_balance'] = np.where(
            df['Drained_after_socializing'] == 'Yes',
            -1 * df['Social_event_attendance'],
            df['Social_event_attendance']
        )
        
        # Digital vs physical preference
        df['digital_preference'] = (
            df['Post_frequency'] / (df['Going_outside'] + df['Social_event_attendance'] + 1e-6)
        )
        
        # Social circle efficiency
        df['social_circle_efficiency'] = (
            df['Post_frequency'] * df['Social_event_attendance'] / 
            (df['Friends_circle_size'] + 1e-6)
        )
        
        logger.info("Behavioral pattern features created")
        return df
    
    def create_ratio_stability_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        PHASE 2: Create ratio stability features to capture behavioral consistency.
        """
        logger.info("Creating ratio stability features...")
        
        df = df.copy()
        
        # Calculate ratios between key features
        numerical_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside',
                         'Friends_circle_size', 'Post_frequency']
        
        ratios = []
        ratio_names = []
        
        for i, col1 in enumerate(numerical_cols):
            for col2 in numerical_cols[i+1:]:
                if col1 in df.columns and col2 in df.columns:
                    ratio = df[col1] / (df[col2] + 1e-6)
                    ratios.append(ratio)
                    ratio_names.append(f'{col1}_to_{col2}_ratio')
        
        # Calculate variance of ratios for each sample (behavioral consistency)
        if ratios:
            ratio_matrix = np.column_stack(ratios)
            df['ratio_variance'] = np.var(ratio_matrix, axis=1)
            df['ratio_std'] = np.std(ratio_matrix, axis=1)
            df['ratio_range'] = np.max(ratio_matrix, axis=1) - np.min(ratio_matrix, axis=1)
            
            # Add top individual ratios
            for ratio, name in zip(ratios[:5], ratio_names[:5]):  # Top 5 ratios
                df[name] = ratio
        
        logger.info("Ratio stability features created")
        return df
    
    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        PHASE 2: Create advanced interaction features.
        """
        logger.info("Creating interaction features...")
        
        df = df.copy()
        
        # Key interaction features
        df['social_to_alone_ratio'] = df['Social_event_attendance'] / (df['Time_spent_Alone'] + 1e-6)
        df['friends_per_post_ratio'] = df['Friends_circle_size'] / (df['Post_frequency'] + 1e-6)
        
        # Social intensity score
        df['social_intensity_score'] = (
            df['Social_event_attendance'] + df['Going_outside'] + 
            df['Friends_circle_size'] + df['Post_frequency']
        ) / 4
        
        # Introversion indicators
        df['introversion_score'] = (
            df['Time_spent_Alone'] + 
            (df['Stage_fear'] == 'Yes').astype(int) + 
            (df['Drained_after_socializing'] == 'Yes').astype(int)
        ) / 3
        
        # Social engagement vs energy drain
        df['social_engagement_vs_drain'] = (
            df['Social_event_attendance'] * (1 - (df['Drained_after_socializing'] == 'Yes').astype(int))
        )
        
        logger.info("Interaction features created")
        return df

    def advanced_feature_engineering(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        PHASE 2: Advanced feature engineering pipeline.
        """
        logger.info("Starting advanced feature engineering...")

        # Store target and IDs
        y_train = train_df['Personality'].copy()

        # Combine for consistent processing
        train_df_fe = train_df.drop(['Personality'], axis=1)
        combined_df = pd.concat([train_df_fe, test_df], ignore_index=True)

        # Basic missing value imputation
        numerical_cols = combined_df.select_dtypes(include=[np.number]).columns.tolist()
        numerical_cols = [col for col in numerical_cols if col not in ['id']]

        if numerical_cols:
            knn_imputer = KNNImputer(n_neighbors=5, weights='distance')
            combined_df[numerical_cols] = knn_imputer.fit_transform(combined_df[numerical_cols])

        # Handle categorical missing values
        categorical_cols = ['Stage_fear', 'Drained_after_socializing']
        for col in categorical_cols:
            if col in combined_df.columns:
                combined_df[col] = combined_df[col].fillna('Unknown')

        # Apply advanced feature engineering
        combined_df = self.create_behavioral_pattern_features(combined_df)
        combined_df = self.create_ratio_stability_features(combined_df)
        combined_df = self.create_interaction_features(combined_df)

        # Handle categorical encoding
        for col in categorical_cols:
            if col in combined_df.columns:
                dummies = pd.get_dummies(combined_df[col], prefix=col, drop_first=True)
                combined_df = pd.concat([combined_df, dummies], axis=1)
                combined_df = combined_df.drop(col, axis=1)

        # Remove ID column
        if 'id' in combined_df.columns:
            combined_df = combined_df.drop('id', axis=1)

        # Ensure all columns are numeric
        for col in combined_df.columns:
            if combined_df[col].dtype == 'object':
                combined_df = combined_df.drop(col, axis=1)

        # Split back to train and test
        n_train = len(train_df_fe)
        X_train = combined_df.iloc[:n_train].copy()
        X_test = combined_df.iloc[n_train:].copy()

        # Add target back to train
        X_train['Personality'] = y_train.values

        logger.info(f"Advanced feature engineering completed. Total features: {len(X_train.columns)-1}")
        return X_train, X_test

    def apply_target_aware_feature_selection(self, X: pd.DataFrame, y: pd.Series,
                                           k_best: int = 50) -> pd.DataFrame:
        """
        PHASE 2: Apply target-aware feature selection using mutual information.
        """
        logger.info(f"Applying target-aware feature selection (k={k_best})...")

        # Encode target for feature selection
        y_encoded = self.label_encoder.fit_transform(y)

        # Remove constant features
        variance_selector = VarianceThreshold(threshold=0.01)
        X_var = variance_selector.fit_transform(X)
        selected_features = X.columns[variance_selector.get_support()]
        X_selected = pd.DataFrame(X_var, columns=selected_features, index=X.index)

        # Mutual information feature selection
        if len(X_selected.columns) > k_best:
            mi_selector = SelectKBest(score_func=mutual_info_classif, k=k_best)
            X_mi = mi_selector.fit_transform(X_selected, y_encoded)
            final_features = X_selected.columns[mi_selector.get_support()]
            X_final = pd.DataFrame(X_mi, columns=final_features, index=X.index)

            logger.info(f"Selected {len(final_features)} features from {len(X.columns)} original features")
            return X_final
        else:
            logger.info(f"Keeping all {len(X_selected.columns)} features (less than k_best)")
            return X_selected

    def train_diverse_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        PHASE 2: Train diverse base models for improved ensemble.
        """
        logger.info("Training diverse base models...")

        # Prepare features
        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)

        models = {}

        # Tree-based models
        logger.info("Training tree-based models...")
        models['xgb'] = XGBClassifier(
            n_estimators=300,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=self.random_state,
            eval_metric='logloss'
        )
        models['xgb'].fit(X_scaled, y_encoded)

        models['lgb'] = LGBMClassifier(
            n_estimators=300,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=self.random_state,
            objective='binary',
            metric='binary_logloss',
            verbose=-1
        )
        models['lgb'].fit(X_scaled, y_encoded)

        models['cat'] = CatBoostClassifier(
            iterations=300,
            depth=6,
            learning_rate=0.1,
            random_state=self.random_state,
            verbose=False,
            eval_metric='Accuracy'
        )
        models['cat'].fit(X_scaled, y_encoded)

        models['rf'] = RandomForestClassifier(
            n_estimators=300,
            max_depth=10,
            min_samples_split=5,
            random_state=self.random_state,
            n_jobs=-1
        )
        models['rf'].fit(X_scaled, y_encoded)

        # PHASE 2: Add diverse model types
        logger.info("Training diverse model types...")

        models['extra_trees'] = ExtraTreesClassifier(
            n_estimators=300,
            max_depth=10,
            min_samples_split=5,
            random_state=self.random_state,
            n_jobs=-1
        )
        models['extra_trees'].fit(X_scaled, y_encoded)

        models['mlp'] = MLPClassifier(
            hidden_layer_sizes=(100, 50),
            max_iter=500,
            random_state=self.random_state,
            early_stopping=True,
            validation_fraction=0.1
        )
        models['mlp'].fit(X_scaled, y_encoded)

        models['svm'] = SVC(
            kernel='rbf',
            C=1.0,
            probability=True,
            random_state=self.random_state
        )
        models['svm'].fit(X_scaled, y_encoded)

        models['nb'] = GaussianNB()
        models['nb'].fit(X_scaled, y_encoded)

        self.models = models
        logger.info(f"Trained {len(models)} diverse models successfully")
        return models

    def create_advanced_stacking(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        PHASE 2: Create advanced stacking ensemble with diverse models.
        """
        logger.info("Creating advanced stacking ensemble...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.transform(y_train)
        X_scaled = self.scaler.transform(X_features)

        # Level 1: All diverse base estimators
        level1_estimators = [
            ('xgb', self.models['xgb']),
            ('lgb', self.models['lgb']),
            ('cat', self.models['cat']),
            ('rf', self.models['rf']),
            ('extra_trees', self.models['extra_trees']),
            ('mlp', self.models['mlp']),
            ('svm', self.models['svm']),
            ('nb', self.models['nb'])
        ]

        # Level 2: Meta-learner with Logistic Regression
        logger.info("Training advanced stacking meta-learner...")
        meta_learner = StackingClassifier(
            estimators=level1_estimators,
            final_estimator=LogisticRegression(
                random_state=self.random_state,
                C=1.0,
                max_iter=1000
            ),
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )
        meta_learner.fit(X_scaled, y_encoded)
        self.models['advanced_stacking'] = meta_learner

        logger.info("Advanced stacking ensemble created successfully")
        return {'level1_count': len(level1_estimators), 'meta_learner': meta_learner}

    def cross_validate_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """Cross-validate all models to measure performance."""
        logger.info("Performing cross-validation...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.transform(y_train)
        X_scaled = self.scaler.transform(X_features)

        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        cv_results = {}

        # Test key models (not all to save time)
        key_models = ['xgb', 'lgb', 'cat', 'rf', 'extra_trees', 'advanced_stacking']

        for name in key_models:
            if name in self.models:
                logger.info(f"Cross-validating {name}...")
                model = self.models[name]
                scores = cross_val_score(model, X_scaled, y_encoded, cv=cv, scoring='accuracy', n_jobs=-1)
                cv_results[name] = {
                    'mean': scores.mean(),
                    'std': scores.std(),
                    'scores': scores
                }
                logger.info(f"{name} CV Score: {scores.mean():.6f} (+/- {scores.std() * 2:.6f})")

        self.cv_scores = cv_results
        return cv_results

    def predict_ensemble(self, X_test: pd.DataFrame) -> np.ndarray:
        """Generate predictions using the advanced stacking ensemble."""
        logger.info("Generating ensemble predictions...")

        X_features = X_test[self.feature_names]
        X_scaled = self.scaler.transform(X_features)

        # Use advanced stacking model for final predictions
        if 'advanced_stacking' in self.models:
            pred_proba = self.models['advanced_stacking'].predict_proba(X_scaled)[:, 1]
            predictions = (pred_proba > self.best_threshold).astype(int)
        else:
            # Fallback to weighted ensemble
            all_proba = []
            weights = []

            for name, model in self.models.items():
                if name != 'advanced_stacking':
                    proba = model.predict_proba(X_scaled)[:, 1]
                    all_proba.append(proba)
                    # Weight by CV performance if available
                    weight = self.cv_scores.get(name, {}).get('mean', 0.5)
                    weights.append(weight)

            if weights:
                weights = np.array(weights) / np.sum(weights)
                ensemble_proba = np.average(all_proba, axis=0, weights=weights)
            else:
                ensemble_proba = np.mean(all_proba, axis=0)

            predictions = (ensemble_proba > self.best_threshold).astype(int)

        logger.info("Ensemble predictions generated successfully")
        return predictions

    def run_phase2_pipeline(self) -> pd.DataFrame:
        """
        Execute Phase 2: Advanced feature engineering + model diversity.
        """
        logger.info("Starting Phase 2 pipeline...")

        # Load data
        train_df, test_df, _ = self.load_data()

        # Advanced feature engineering
        X_train_fe, X_test_fe = self.advanced_feature_engineering(train_df, test_df)

        # Split for validation
        y_train = X_train_fe['Personality']
        X_train_features = X_train_fe.drop('Personality', axis=1)

        # Apply target-aware feature selection
        X_train_selected = self.apply_target_aware_feature_selection(X_train_features, y_train, k_best=60)

        # Update feature names after selection
        self.feature_names = X_train_selected.columns.tolist()
        X_test_selected = X_test_fe[self.feature_names]

        # Train diverse models
        self.train_diverse_models(X_train_selected, y_train)

        # Create advanced stacking
        self.create_advanced_stacking(X_train_selected, y_train)

        # Cross-validation
        self.cross_validate_models(X_train_selected, y_train)

        # Generate predictions
        test_predictions = self.predict_ensemble(X_test_selected)
        test_predictions_labels = self.label_encoder.inverse_transform(test_predictions)

        # Create submission
        test_ids = test_df['id'].values
        submission_df = pd.DataFrame({
            'id': test_ids,
            'Personality': test_predictions_labels
        })

        logger.info("Phase 2 pipeline completed successfully!")
        return submission_df


def main():
    """Execute Phase 2 improvements."""
    print("=" * 60)
    print("PHASE 2: ADVANCED FEATURES + MODEL DIVERSITY")
    print("Building on Phase 1 (0.974898)")
    print("Expected improvement: +0.002 to +0.005 accuracy → 0.977+")
    print("=" * 60)

    # Initialize predictor
    predictor = Phase2PersonalityPredictor(random_state=42)

    try:
        # Run Phase 2 pipeline
        submission_df = predictor.run_phase2_pipeline()

        # Save submission
        submission_filename = 'phase2_submission.csv'
        submission_df.to_csv(submission_filename, index=False)

        print("\n" + "=" * 60)
        print("PHASE 2 COMPLETED SUCCESSFULLY!")
        print(f"Submission saved as: {submission_filename}")
        print(f"Features used: {len(predictor.feature_names)}")
        print(f"Models trained: {len(predictor.models)}")

        # Display CV scores
        if predictor.cv_scores:
            print("\nCross-Validation Results:")
            print("-" * 40)
            for model_name, scores in predictor.cv_scores.items():
                print(f"{model_name:20s}: {scores['mean']:.6f} (+/- {scores['std']*2:.6f})")

        print("\nPHASE 2 IMPROVEMENTS IMPLEMENTED:")
        print("✓ Behavioral pattern features (social anxiety, extroversion tendency)")
        print("✓ Ratio stability features (behavioral consistency)")
        print("✓ Advanced interaction features")
        print("✓ Target-aware feature selection (mutual information)")
        print("✓ Model diversity (8 models: XGB, LGB, Cat, RF, ExtraTrees, MLP, SVM, NB)")
        print("✓ Advanced stacking ensemble")

        print(f"\nExpected improvement: +0.002 to +0.005 accuracy")
        print("Target: 0.977+ (from Phase 1: 0.974898)")

    except Exception as e:
        logger.error(f"Phase 2 pipeline failed: {str(e)}")
        raise

    return submission_df


if __name__ == "__main__":
    submission = main()
    print("\n🎯 PHASE 2 READY FOR TESTING!")
    print("File: phase2_submission.csv")
    print("Expected: 0.977+ accuracy (improvement over Phase 1: 0.974898)")
