#!/usr/bin/env python3
"""
PHASE 1: HIGH-IMPACT IMPROVEMENTS - PSEUDO-LABELING + MULTI-LEVEL STACKING
Focus on the top 2 strategies first before adding more complexity.

Expected improvements:
- Pseudo-labeling: +0.002 to +0.005 accuracy
- Multi-level stacking: +0.001 to +0.002 accuracy
Total expected: +0.003 to +0.007 accuracy boost
"""

import numpy as np
import pandas as pd
import warnings
from tqdm import tqdm
import logging
from typing import Tuple, Dict

# Core ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.impute import KNNImputer
from sklearn.ensemble import StackingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score

# Advanced models
from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedPersonalityPredictor:
    """
    Phase 1: Focus on pseudo-labeling and multi-level stacking only.
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.feature_names = []
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.best_threshold = 0.5
        self.cv_scores = {}
        
        np.random.seed(random_state)
        
        print("=== PHASE 1: PSEUDO-LABELING + MULTI-LEVEL STACKING ===")
        print("Target: +0.003 to +0.007 accuracy improvement")
        print("Current baseline: 0.976518 → Target: 0.980+")
        print("=" * 55)
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load and merge training and test data with personality dataset."""
        logger.info("Loading data files...")
        
        train_df = pd.read_csv("train.csv")
        test_df = pd.read_csv("test.csv")
        
        try:
            dataset_df = pd.read_csv("personality_dataset.csv")
        except FileNotFoundError:
            dataset_df = pd.read_csv("personality_datasert.csv")
        
        logger.info(f"Train shape: {train_df.shape}, Test shape: {test_df.shape}")
        
        # Prepare dataset for merging
        dataset_df = (
            dataset_df
            .rename(columns={'Personality': 'match_p'})
            .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                              'Going_outside', 'Drained_after_socializing', 
                              'Friends_circle_size', 'Post_frequency'])
        )
        
        # Merge columns
        merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency']
        
        # Merge datasets
        train_df = train_df.merge(dataset_df, how='left', on=merge_cols)
        test_df = test_df.merge(dataset_df, how='left', on=merge_cols)
        
        logger.info("Data loading and merging completed successfully")
        return train_df, test_df, dataset_df
    
    def basic_feature_engineering(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Basic feature engineering - keep it simple for Phase 1.
        """
        logger.info("Applying basic feature engineering...")
        
        # Store target and IDs
        y_train = train_df['Personality'].copy()
        train_ids = train_df['id'].copy()
        test_ids = test_df['id'].copy()
        
        # Combine for consistent processing
        train_df_fe = train_df.drop(['Personality'], axis=1)
        combined_df = pd.concat([train_df_fe, test_df], ignore_index=True)
        
        # Basic missing value imputation
        numerical_cols = combined_df.select_dtypes(include=[np.number]).columns.tolist()
        numerical_cols = [col for col in numerical_cols if col not in ['id']]
        
        if numerical_cols:
            knn_imputer = KNNImputer(n_neighbors=5, weights='distance')
            combined_df[numerical_cols] = knn_imputer.fit_transform(combined_df[numerical_cols])
        
        # Handle categorical missing values
        categorical_cols = ['Stage_fear', 'Drained_after_socializing']
        for col in categorical_cols:
            if col in combined_df.columns:
                combined_df[col] = combined_df[col].fillna('Unknown')
        
        # Basic interaction features
        combined_df['social_to_alone_ratio'] = (
            combined_df['Social_event_attendance'] / (combined_df['Time_spent_Alone'] + 1e-6)
        )
        combined_df['friends_per_post_ratio'] = (
            combined_df['Friends_circle_size'] / (combined_df['Post_frequency'] + 1e-6)
        )
        combined_df['social_intensity_score'] = (
            combined_df['Social_event_attendance'] + combined_df['Going_outside'] +
            combined_df['Friends_circle_size'] + combined_df['Post_frequency']
        ) / 4
        
        # Handle categorical encoding
        for col in categorical_cols:
            if col in combined_df.columns:
                dummies = pd.get_dummies(combined_df[col], prefix=col, drop_first=True)
                combined_df = pd.concat([combined_df, dummies], axis=1)
                combined_df = combined_df.drop(col, axis=1)
        
        # Remove ID column
        if 'id' in combined_df.columns:
            combined_df = combined_df.drop('id', axis=1)
        
        # Ensure all columns are numeric
        for col in combined_df.columns:
            if combined_df[col].dtype == 'object':
                combined_df = combined_df.drop(col, axis=1)
        
        # Split back to train and test
        n_train = len(train_df_fe)
        X_train = combined_df.iloc[:n_train].copy()
        X_test = combined_df.iloc[n_train:].copy()
        
        # Add target back to train
        X_train['Personality'] = y_train.values
        
        # Store feature names
        self.feature_names = [col for col in X_train.columns if col != 'Personality']
        
        logger.info(f"Basic feature engineering completed. Feature count: {len(self.feature_names)}")
        return X_train, X_test

    def train_base_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """Train core base models for stacking."""
        logger.info("Training base models...")

        # Prepare features
        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)

        models = {}

        # XGBoost
        logger.info("Training XGBoost...")
        models['xgb'] = XGBClassifier(
            n_estimators=500,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=self.random_state,
            eval_metric='logloss'
        )
        models['xgb'].fit(X_scaled, y_encoded)

        # LightGBM
        logger.info("Training LightGBM...")
        models['lgb'] = LGBMClassifier(
            n_estimators=500,
            max_depth=6,
            learning_rate=0.1,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=self.random_state,
            objective='binary',
            metric='binary_logloss',
            verbose=-1
        )
        models['lgb'].fit(X_scaled, y_encoded)

        # CatBoost
        logger.info("Training CatBoost...")
        models['cat'] = CatBoostClassifier(
            iterations=500,
            depth=6,
            learning_rate=0.1,
            random_state=self.random_state,
            verbose=False,
            eval_metric='Accuracy'
        )
        models['cat'].fit(X_scaled, y_encoded)

        # Random Forest
        logger.info("Training Random Forest...")
        models['rf'] = RandomForestClassifier(
            n_estimators=500,
            max_depth=10,
            min_samples_split=5,
            random_state=self.random_state,
            n_jobs=-1
        )
        models['rf'].fit(X_scaled, y_encoded)

        self.models = models
        logger.info(f"Trained {len(models)} base models successfully")
        return models

    def apply_pseudo_labeling(self, X_train: pd.DataFrame, y_train: pd.Series,
                            X_test: pd.DataFrame, confidence_threshold: float = 0.9) -> Tuple[pd.DataFrame, pd.Series]:
        """
        PHASE 1: Pseudo-labeling with high-confidence predictions (optimized).
        """
        logger.info(f"Applying pseudo-labeling (threshold: {confidence_threshold})...")

        X_train_features = X_train[self.feature_names]
        X_test_features = X_test[self.feature_names]
        X_test_scaled = self.scaler.transform(X_test_features)

        # Get predictions from all base models at once (vectorized)
        all_predictions = []
        all_confidences = []

        for model_name, model in self.models.items():
            logger.info(f"Getting predictions from {model_name}...")
            pred_proba = model.predict_proba(X_test_scaled)
            max_probs = np.max(pred_proba, axis=1)
            pred_classes = np.argmax(pred_proba, axis=1)

            all_predictions.append(pred_classes)
            all_confidences.append(max_probs)

        # Convert to arrays for easier processing
        all_predictions = np.array(all_predictions)  # Shape: (n_models, n_samples)
        all_confidences = np.array(all_confidences)  # Shape: (n_models, n_samples)

        # Find samples where models agree with high confidence
        pseudo_labels = []
        pseudo_indices = []

        for i in range(len(X_test)):
            # Get high-confidence predictions for this sample
            high_conf_mask = all_confidences[:, i] >= confidence_threshold
            high_conf_preds = all_predictions[high_conf_mask, i]

            # If at least 2 models have high confidence and all agree
            if len(high_conf_preds) >= 2:
                unique_preds = np.unique(high_conf_preds)
                if len(unique_preds) == 1:  # All high-confidence models agree
                    pseudo_labels.append(unique_preds[0])
                    pseudo_indices.append(i)

        logger.info(f"Generated {len(pseudo_labels)} pseudo-labels from {len(X_test)} test samples")

        if len(pseudo_labels) > 0:
            # Create augmented training set
            pseudo_X = X_test_features.iloc[pseudo_indices].copy()
            pseudo_y = pd.Series(pseudo_labels, index=pseudo_X.index)
            pseudo_y_original = self.label_encoder.inverse_transform(pseudo_y)
            pseudo_y_series = pd.Series(pseudo_y_original, index=pseudo_X.index)

            # Combine with original training data
            augmented_X = pd.concat([X_train_features, pseudo_X], ignore_index=True)
            augmented_y = pd.concat([y_train, pseudo_y_series], ignore_index=True)

            logger.info(f"Augmented training set: {len(augmented_X)} samples (original: {len(X_train_features)}, pseudo: {len(pseudo_X)})")
            return augmented_X, augmented_y
        else:
            logger.warning("No high-confidence pseudo-labels generated")
            return X_train_features, y_train

    def create_multi_level_stacking(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        PHASE 1: Simple 2-level stacking (base models → meta-learner).
        """
        logger.info("Creating multi-level stacking ensemble...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.transform(y_train)
        X_scaled = self.scaler.transform(X_features)

        # Level 1: Base estimators
        level1_estimators = [
            ('xgb', self.models['xgb']),
            ('lgb', self.models['lgb']),
            ('cat', self.models['cat']),
            ('rf', self.models['rf'])
        ]

        # Level 2: Meta-learner with Logistic Regression
        logger.info("Training Level 2 meta-learner...")
        meta_learner = StackingClassifier(
            estimators=level1_estimators,
            final_estimator=LogisticRegression(
                random_state=self.random_state,
                C=1.0,
                max_iter=1000
            ),
            cv=5,
            stack_method='predict_proba',
            n_jobs=-1
        )
        meta_learner.fit(X_scaled, y_encoded)
        self.models['stacking'] = meta_learner

        logger.info("Multi-level stacking ensemble created successfully")
        return {'level1_count': len(level1_estimators), 'meta_learner': meta_learner}

    def cross_validate_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """Cross-validate all models to measure performance."""
        logger.info("Performing cross-validation...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.transform(y_train)
        X_scaled = self.scaler.transform(X_features)

        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        cv_results = {}

        for name, model in self.models.items():
            logger.info(f"Cross-validating {name}...")
            scores = cross_val_score(model, X_scaled, y_encoded, cv=cv, scoring='accuracy', n_jobs=-1)
            cv_results[name] = {
                'mean': scores.mean(),
                'std': scores.std(),
                'scores': scores
            }
            logger.info(f"{name} CV Score: {scores.mean():.6f} (+/- {scores.std() * 2:.6f})")

        self.cv_scores = cv_results
        return cv_results

    def predict_ensemble(self, X_test: pd.DataFrame) -> np.ndarray:
        """Generate predictions using the stacking ensemble."""
        logger.info("Generating ensemble predictions...")

        X_features = X_test[self.feature_names]
        X_scaled = self.scaler.transform(X_features)

        # Use stacking model for final predictions
        if 'stacking' in self.models:
            pred_proba = self.models['stacking'].predict_proba(X_scaled)[:, 1]
            predictions = (pred_proba > self.best_threshold).astype(int)
        else:
            # Fallback to simple ensemble
            all_proba = []
            for name, model in self.models.items():
                if name != 'stacking':
                    proba = model.predict_proba(X_scaled)[:, 1]
                    all_proba.append(proba)

            ensemble_proba = np.mean(all_proba, axis=0)
            predictions = (ensemble_proba > self.best_threshold).astype(int)

        logger.info("Ensemble predictions generated successfully")
        return predictions

    def run_phase1_pipeline(self) -> pd.DataFrame:
        """
        Execute Phase 1: Pseudo-labeling + Multi-level stacking only.
        """
        logger.info("Starting Phase 1 pipeline...")

        # Load data
        train_df, test_df, _ = self.load_data()

        # Basic feature engineering
        X_train_fe, X_test_fe = self.basic_feature_engineering(train_df, test_df)

        # Split for validation
        y_train = X_train_fe['Personality']
        X_train_features = X_train_fe.drop('Personality', axis=1)

        X_train_split, X_val_split, y_train_split, y_val_split = train_test_split(
            X_train_features, y_train, test_size=0.2, random_state=self.random_state,
            stratify=y_train
        )

        # Train base models
        self.train_base_models(X_train_split, y_train_split)

        # Apply pseudo-labeling
        logger.info("Applying pseudo-labeling...")
        X_augmented, y_augmented = self.apply_pseudo_labeling(
            X_train_split, y_train_split, X_test_fe, confidence_threshold=0.85
        )

        # Retrain with augmented data if pseudo-labels were generated
        if len(X_augmented) > len(X_train_split):
            logger.info("Retraining with pseudo-labeled data...")
            self.train_base_models(X_augmented, y_augmented)

        # Create multi-level stacking
        self.create_multi_level_stacking(X_train_split, y_train_split)

        # Cross-validation
        self.cross_validate_models(X_train_features, y_train)

        # Final training on full dataset
        logger.info("Final training on full dataset...")
        self.train_base_models(X_train_features, y_train)
        self.create_multi_level_stacking(X_train_features, y_train)

        # Generate predictions
        test_predictions = self.predict_ensemble(X_test_fe)
        test_predictions_labels = self.label_encoder.inverse_transform(test_predictions)

        # Create submission
        test_ids = test_df['id'].values
        submission_df = pd.DataFrame({
            'id': test_ids,
            'Personality': test_predictions_labels
        })

        logger.info("Phase 1 pipeline completed successfully!")
        return submission_df


def main():
    """Execute Phase 1 improvements."""
    print("=" * 60)
    print("PHASE 1: PSEUDO-LABELING + MULTI-LEVEL STACKING")
    print("Expected improvement: +0.003 to +0.007 accuracy")
    print("Current baseline: 0.976518 → Target: 0.980+")
    print("=" * 60)

    # Initialize predictor
    predictor = ImprovedPersonalityPredictor(random_state=42)

    try:
        # Run Phase 1 pipeline
        submission_df = predictor.run_phase1_pipeline()

        # Save submission
        submission_filename = 'phase1_submission.csv'
        submission_df.to_csv(submission_filename, index=False)

        print("\n" + "=" * 60)
        print("PHASE 1 COMPLETED SUCCESSFULLY!")
        print(f"Submission saved as: {submission_filename}")
        print(f"Features used: {len(predictor.feature_names)}")
        print(f"Models trained: {len(predictor.models)}")

        # Display CV scores
        if predictor.cv_scores:
            print("\nCross-Validation Results:")
            print("-" * 40)
            for model_name, scores in predictor.cv_scores.items():
                print(f"{model_name:15s}: {scores['mean']:.6f} (+/- {scores['std']*2:.6f})")

        print("\nPHASE 1 IMPROVEMENTS IMPLEMENTED:")
        print("✓ Pseudo-labeling with high-confidence predictions")
        print("✓ Multi-level stacking (4 base models → meta-learner)")
        print("✓ Basic feature engineering with interaction features")
        print("✓ Cross-validation for performance measurement")

        print(f"\nExpected improvement: +0.003 to +0.007 accuracy")
        print("Ready to test before implementing Phase 2!")

    except Exception as e:
        logger.error(f"Phase 1 pipeline failed: {str(e)}")
        raise

    return submission_df


if __name__ == "__main__":
    submission = main()
    print("\n🎯 PHASE 1 READY FOR TESTING!")
    print("File: phase1_submission.csv")
    print("Next: Validate results before implementing Phase 2")
