#!/usr/bin/env python3
"""
SIMPLE TEST: Basic stacking without pseudo-labeling first
Test the core pipeline before adding pseudo-labeling complexity.
"""

import numpy as np
import pandas as pd
import warnings
import logging
from typing import Tuple

# Core ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.impute import KNNImputer
from sklearn.ensemble import StackingClassifier, RandomForestClassifier
from sklearn.linear_model import LogisticRegression

# Advanced models
from xgboost import XGBClassifier
from catboost import CatBoostClassifier
from lightgbm import LGBMClassifier

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplePhase1Test:
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.feature_names = []
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        
        print("=== SIMPLE PHASE 1 TEST ===")
        print("Testing basic stacking without pseudo-labeling")
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Load and merge data."""
        logger.info("Loading data...")
        
        train_df = pd.read_csv("train.csv")
        test_df = pd.read_csv("test.csv")
        
        try:
            dataset_df = pd.read_csv("personality_dataset.csv")
        except FileNotFoundError:
            dataset_df = pd.read_csv("personality_datasert.csv")
        
        # Prepare dataset for merging
        dataset_df = dataset_df.rename(columns={'Personality': 'match_p'}).drop_duplicates([
            'Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
            'Going_outside', 'Drained_after_socializing', 
            'Friends_circle_size', 'Post_frequency'
        ])
        
        # Merge
        merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency']
        
        train_df = train_df.merge(dataset_df, how='left', on=merge_cols)
        test_df = test_df.merge(dataset_df, how='left', on=merge_cols)
        
        logger.info(f"Data loaded: Train {train_df.shape}, Test {test_df.shape}")
        return train_df, test_df
    
    def prepare_features(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Simple feature preparation."""
        logger.info("Preparing features...")
        
        # Store target
        y_train = train_df['Personality'].copy()
        
        # Combine for processing
        train_features = train_df.drop(['Personality'], axis=1)
        combined = pd.concat([train_features, test_df], ignore_index=True)
        
        # Basic imputation
        numerical_cols = combined.select_dtypes(include=[np.number]).columns.tolist()
        numerical_cols = [col for col in numerical_cols if col not in ['id']]
        
        if numerical_cols:
            imputer = KNNImputer(n_neighbors=5)
            combined[numerical_cols] = imputer.fit_transform(combined[numerical_cols])
        
        # Handle categoricals
        for col in ['Stage_fear', 'Drained_after_socializing']:
            if col in combined.columns:
                combined[col] = combined[col].fillna('Unknown')
                dummies = pd.get_dummies(combined[col], prefix=col, drop_first=True)
                combined = pd.concat([combined, dummies], axis=1)
                combined = combined.drop(col, axis=1)
        
        # Simple interactions
        combined['social_ratio'] = combined['Social_event_attendance'] / (combined['Time_spent_Alone'] + 1e-6)
        combined['social_score'] = (combined['Social_event_attendance'] + combined['Going_outside'] + 
                                   combined['Friends_circle_size'] + combined['Post_frequency']) / 4
        
        # Remove ID and non-numeric columns
        if 'id' in combined.columns:
            combined = combined.drop('id', axis=1)
        
        for col in combined.columns:
            if combined[col].dtype == 'object':
                combined = combined.drop(col, axis=1)
        
        # Split back
        n_train = len(train_features)
        X_train = combined.iloc[:n_train].copy()
        X_test = combined.iloc[n_train:].copy()
        
        X_train['Personality'] = y_train.values
        self.feature_names = [col for col in X_train.columns if col != 'Personality']
        
        logger.info(f"Features prepared: {len(self.feature_names)} features")
        return X_train, X_test
    
    def train_models(self, X_train: pd.DataFrame, y_train: pd.Series):
        """Train base models."""
        logger.info("Training models...")
        
        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)
        
        # Simple models
        self.models['xgb'] = XGBClassifier(n_estimators=100, random_state=self.random_state, eval_metric='logloss')
        self.models['lgb'] = LGBMClassifier(n_estimators=100, random_state=self.random_state, verbose=-1)
        self.models['rf'] = RandomForestClassifier(n_estimators=100, random_state=self.random_state)
        
        for name, model in self.models.items():
            logger.info(f"Training {name}...")
            model.fit(X_scaled, y_encoded)
        
        # Create stacking
        logger.info("Creating stacking ensemble...")
        estimators = [(name, model) for name, model in self.models.items()]
        self.stacking = StackingClassifier(
            estimators=estimators,
            final_estimator=LogisticRegression(random_state=self.random_state),
            cv=3
        )
        self.stacking.fit(X_scaled, y_encoded)
        
        logger.info("Models trained successfully")
    
    def run_test(self):
        """Run simple test."""
        logger.info("Starting simple test...")
        
        # Load data
        train_df, test_df = self.load_data()
        
        # Prepare features
        X_train, X_test = self.prepare_features(train_df, test_df)
        
        # Train models
        y_train = X_train['Personality']
        X_train_features = X_train.drop('Personality', axis=1)
        self.train_models(X_train_features, y_train)
        
        # Cross-validation
        logger.info("Cross-validation...")
        X_scaled = self.scaler.transform(X_train_features)
        y_encoded = self.label_encoder.transform(y_train)
        
        cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=self.random_state)
        cv_score = cross_val_score(self.stacking, X_scaled, y_encoded, cv=cv, scoring='accuracy')
        
        logger.info(f"CV Score: {cv_score.mean():.6f} (+/- {cv_score.std()*2:.6f})")
        
        # Predictions
        logger.info("Generating predictions...")
        X_test_scaled = self.scaler.transform(X_test)
        predictions = self.stacking.predict(X_test_scaled)
        pred_labels = self.label_encoder.inverse_transform(predictions)
        
        # Submission
        submission = pd.DataFrame({
            'id': test_df['id'].values,
            'Personality': pred_labels
        })
        
        submission.to_csv('simple_test_submission.csv', index=False)
        logger.info("Test completed! Submission saved as simple_test_submission.csv")
        
        return submission

if __name__ == "__main__":
    tester = SimplePhase1Test()
    result = tester.run_test()
    print("\n✅ SIMPLE TEST COMPLETED!")
    print("File: simple_test_submission.csv")
    print("If this works, we can add pseudo-labeling next.")
