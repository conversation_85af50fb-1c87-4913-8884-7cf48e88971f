#!/usr/bin/env python3
"""
PHASE 3: PSEUDO-LABELING + HYPERPARAMETER OPTIMIZATION + ADVANCED VALIDATION
Building on Phase 2 (0.975708) with:
- Pseudo-labeling with high-confidence predictions
- Bayesian hyperparameter optimization (Optuna)
- RepeatedStratifiedKFold validation
- Model calibration

Expected improvements:
- Pseudo-labeling: +0.001 to +0.003 accuracy
- Hyperparameter optimization: +0.001 to +0.002 accuracy
- Advanced validation: +0.0005 to +0.001 accuracy
Total expected: +0.0025 to +0.006 accuracy boost (target: 0.978+)
"""

import numpy as np
import pandas as pd
import warnings
import logging
import gc
from typing import Tuple, Dict
from tqdm import tqdm

# Core ML libraries
from sklearn.model_selection import train_test_split, StratifiedKFold, RepeatedStratifiedKFold, cross_val_score
from sklearn.preprocessing import LabelEncoder, StandardScaler
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neural_network import MLPClassifier
from sklearn.svm import SVC
from sklearn.naive_bayes import GaussianNB
from sklearn.feature_selection import SelectKBest, mutual_info_classif, VarianceThreshold
from sklearn.impute import KNNImputer
from sklearn.calibration import CalibratedClassifierCV
from sklearn.metrics import accuracy_score

# Gradient boosting
from xgboost import XGBClassifier
from lightgbm import LGBMClassifier
from catboost import CatBoostClassifier

# Hyperparameter optimization
try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False
    print("Optuna not available - using default parameters")

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Phase3PersonalityPredictor:
    """
    Phase 3: Pseudo-labeling + Hyperparameter optimization + Advanced validation
    """
    
    def __init__(self, random_state: int = 42):
        self.random_state = random_state
        self.models = {}
        self.feature_names = []
        self.label_encoder = LabelEncoder()
        self.scaler = StandardScaler()
        self.best_threshold = 0.5
        self.cv_scores = {}
        self.best_params = {}
        
        np.random.seed(random_state)
        
        print("=== PHASE 3: PSEUDO-LABELING + HYPERPARAMETER OPTIMIZATION ===")
        print("Building on Phase 2 (0.975708)")
        print("Target: +0.0025 to +0.006 accuracy improvement → 0.978+")
        print("=" * 65)
    
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load and merge training and test data with personality dataset."""
        logger.info("Loading data files...")
        
        train_df = pd.read_csv("train.csv")
        test_df = pd.read_csv("test.csv")
        
        try:
            dataset_df = pd.read_csv("personality_dataset.csv")
        except FileNotFoundError:
            dataset_df = pd.read_csv("personality_datasert.csv")
        
        logger.info(f"Train shape: {train_df.shape}, Test shape: {test_df.shape}")
        
        # Prepare dataset for merging
        dataset_df = (
            dataset_df
            .rename(columns={'Personality': 'match_p'})
            .drop_duplicates(['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                              'Going_outside', 'Drained_after_socializing', 
                              'Friends_circle_size', 'Post_frequency'])
        )
        
        # Merge with dataset
        merge_cols = ['Time_spent_Alone', 'Stage_fear', 'Social_event_attendance',
                      'Going_outside', 'Drained_after_socializing', 
                      'Friends_circle_size', 'Post_frequency']
        
        train_merged = train_df.merge(dataset_df, on=merge_cols, how='left')
        test_merged = test_df.merge(dataset_df, on=merge_cols, how='left')
        
        logger.info(f"Merged train shape: {train_merged.shape}, test shape: {test_merged.shape}")
        return train_merged, test_merged, dataset_df

    def phase3_feature_engineering(self, train_df: pd.DataFrame, test_df: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        PHASE 3: Enhanced feature engineering building on Phase 2.
        """
        logger.info("Phase 3 feature engineering...")
        
        # Store target and IDs
        y_train = train_df['Personality'].copy()
        
        # Combine for consistent processing
        train_df_fe = train_df.drop(['Personality'], axis=1)
        combined_df = pd.concat([train_df_fe, test_df], ignore_index=True)
        
        # Handle missing values with KNN imputation
        numerical_cols = ['Time_spent_Alone', 'Social_event_attendance', 'Going_outside',
                         'Friends_circle_size', 'Post_frequency']
        
        if numerical_cols:
            knn_imputer = KNNImputer(n_neighbors=5, weights='distance')
            combined_df[numerical_cols] = knn_imputer.fit_transform(combined_df[numerical_cols])
        
        # Handle categorical missing values
        categorical_cols = ['Stage_fear', 'Drained_after_socializing']
        for col in categorical_cols:
            if col in combined_df.columns:
                combined_df[col] = combined_df[col].fillna('Unknown')
        
        # PHASE 3: Enhanced behavioral pattern features
        logger.info("Creating enhanced behavioral patterns...")
        
        # Social anxiety indicator (enhanced)
        combined_df['social_anxiety_indicator'] = (
            (combined_df['Stage_fear'] == 'Yes') & 
            (combined_df['Social_event_attendance'] < combined_df['Social_event_attendance'].median())
        ).astype(int)
        
        # Extroversion tendency score (enhanced)
        combined_df['extroversion_tendency'] = (
            combined_df['Social_event_attendance'] + 
            combined_df['Going_outside'] + 
            combined_df['Friends_circle_size'] + 
            combined_df['Post_frequency'] - 
            combined_df['Time_spent_Alone']
        ) / 5
        
        # Social energy balance
        combined_df['social_energy_balance'] = np.where(
            combined_df['Drained_after_socializing'] == 'Yes',
            -1 * combined_df['Social_event_attendance'],
            combined_df['Social_event_attendance']
        )
        
        # Digital vs physical preference
        combined_df['digital_preference'] = (
            combined_df['Post_frequency'] / (combined_df['Going_outside'] + combined_df['Social_event_attendance'] + 1e-6)
        )
        
        # Social circle efficiency
        combined_df['social_circle_efficiency'] = (
            combined_df['Post_frequency'] * combined_df['Social_event_attendance'] / 
            (combined_df['Friends_circle_size'] + 1e-6)
        )
        
        # PHASE 3: Advanced ratio features
        logger.info("Creating advanced ratio features...")
        
        # Key ratios
        combined_df['social_to_alone_ratio'] = (
            combined_df['Social_event_attendance'] / (combined_df['Time_spent_Alone'] + 1e-6)
        )
        combined_df['friends_per_post_ratio'] = (
            combined_df['Friends_circle_size'] / (combined_df['Post_frequency'] + 1e-6)
        )
        combined_df['going_out_to_friends_ratio'] = (
            combined_df['Going_outside'] / (combined_df['Friends_circle_size'] + 1e-6)
        )
        combined_df['social_events_to_posts_ratio'] = (
            combined_df['Social_event_attendance'] / (combined_df['Post_frequency'] + 1e-6)
        )
        
        # Social intensity score
        combined_df['social_intensity_score'] = (
            combined_df['Social_event_attendance'] + combined_df['Going_outside'] + 
            combined_df['Friends_circle_size'] + combined_df['Post_frequency']
        ) / 4
        
        # Introversion indicators
        combined_df['introversion_score'] = (
            combined_df['Time_spent_Alone'] + 
            (combined_df['Stage_fear'] == 'Yes').astype(int) + 
            (combined_df['Drained_after_socializing'] == 'Yes').astype(int)
        ) / 3
        
        # PHASE 3: New interaction features
        logger.info("Creating new interaction features...")
        
        # Behavioral consistency score
        combined_df['behavioral_consistency'] = (
            combined_df['Social_event_attendance'] * combined_df['Going_outside'] / 
            (combined_df['Time_spent_Alone'] + 1e-6)
        )
        
        # Social media engagement ratio
        combined_df['social_media_engagement'] = (
            combined_df['Post_frequency'] / (combined_df['Friends_circle_size'] + 1e-6)
        )
        
        # Energy drain indicator
        combined_df['energy_drain_score'] = (
            (combined_df['Drained_after_socializing'] == 'Yes').astype(int) * 
            combined_df['Social_event_attendance']
        )
        
        # Encode categorical variables
        categorical_columns = ['Stage_fear', 'Drained_after_socializing']
        for col in categorical_columns:
            if col in combined_df.columns:
                combined_df[col] = combined_df[col].map({'Yes': 1, 'No': 0, 'Unknown': -1})
        
        # Store feature names
        feature_cols = [col for col in combined_df.columns if col not in ['id', 'match_p']]
        self.feature_names = feature_cols
        
        # Split back
        train_size = len(train_df_fe)
        X_train = combined_df.iloc[:train_size].copy()
        X_test = combined_df.iloc[train_size:].copy()
        
        # Add target back to training data
        X_train['Personality'] = y_train.values
        
        logger.info(f"Phase 3 feature engineering completed. Features: {len(self.feature_names)}")
        return X_train, X_test
    
    def apply_feature_selection(self, X: pd.DataFrame, y: pd.Series, k_best: int = 50) -> pd.DataFrame:
        """
        PHASE 3: Apply enhanced feature selection.
        """
        logger.info(f"Applying enhanced feature selection (k={k_best})...")
        
        # Encode target for feature selection
        y_encoded = self.label_encoder.fit_transform(y)
        
        # Remove constant features
        variance_selector = VarianceThreshold(threshold=0.01)
        X_var = variance_selector.fit_transform(X)
        selected_features = X.columns[variance_selector.get_support()]
        X_selected = pd.DataFrame(X_var, columns=selected_features, index=X.index)
        
        # Mutual information feature selection
        if len(X_selected.columns) > k_best:
            mi_selector = SelectKBest(score_func=mutual_info_classif, k=k_best)
            X_mi = mi_selector.fit_transform(X_selected, y_encoded)
            final_features = X_selected.columns[mi_selector.get_support()]
            X_final = pd.DataFrame(X_mi, columns=final_features, index=X.index)
            
            logger.info(f"Selected {len(final_features)} features from {len(X.columns)} original features")
            return X_final
        else:
            logger.info(f"Keeping all {len(X_selected.columns)} features (less than k_best)")
            return X_selected

    def optimize_hyperparameters(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        PHASE 3: Bayesian hyperparameter optimization using Optuna.
        """
        if not OPTUNA_AVAILABLE:
            logger.warning("Optuna not available, using default parameters")
            return {}

        logger.info("Starting Bayesian hyperparameter optimization...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)

        def objective(trial):
            # XGBoost parameters
            xgb_params = {
                'n_estimators': trial.suggest_int('xgb_n_estimators', 400, 800),
                'max_depth': trial.suggest_int('xgb_max_depth', 5, 10),
                'learning_rate': trial.suggest_float('xgb_learning_rate', 0.05, 0.15),
                'subsample': trial.suggest_float('xgb_subsample', 0.7, 0.9),
                'colsample_bytree': trial.suggest_float('xgb_colsample_bytree', 0.7, 0.9),
                'random_state': self.random_state,
                'eval_metric': 'logloss',
                'device': 'cuda'
            }

            # LightGBM parameters
            lgb_params = {
                'n_estimators': trial.suggest_int('lgb_n_estimators', 400, 800),
                'max_depth': trial.suggest_int('lgb_max_depth', 5, 10),
                'learning_rate': trial.suggest_float('lgb_learning_rate', 0.05, 0.15),
                'subsample': trial.suggest_float('lgb_subsample', 0.7, 0.9),
                'colsample_bytree': trial.suggest_float('lgb_colsample_bytree', 0.7, 0.9),
                'random_state': self.random_state,
                'objective': 'binary',
                'metric': 'binary_logloss',
                'verbose': -1
            }

            # Create models with trial parameters
            models = {
                'xgb': XGBClassifier(**xgb_params),
                'lgb': LGBMClassifier(**lgb_params)
            }

            # Cross-validation
            cv = StratifiedKFold(n_splits=3, shuffle=True, random_state=self.random_state)
            scores = []

            for model in models.values():
                model_scores = cross_val_score(model, X_scaled, y_encoded, cv=cv, scoring='accuracy', n_jobs=1)
                scores.append(model_scores.mean())

            return np.mean(scores)

        # Run optimization
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=50, timeout=600)  # 10 minutes max

        self.best_params = study.best_params
        logger.info(f"Best hyperparameters found with score: {study.best_value:.6f}")

        return self.best_params

    def apply_pseudo_labeling(self, X_train: pd.DataFrame, y_train: pd.Series,
                             X_test: pd.DataFrame, confidence_threshold: float = 0.9) -> Tuple[pd.DataFrame, pd.Series]:
        """
        PHASE 3: Apply pseudo-labeling with high-confidence predictions.
        """
        logger.info(f"Applying pseudo-labeling with confidence threshold: {confidence_threshold}")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)

        # Train base models for pseudo-labeling
        base_models = {
            'xgb': XGBClassifier(n_estimators=500, max_depth=7, learning_rate=0.08,
                                random_state=self.random_state, device='cuda'),
            'lgb': LGBMClassifier(n_estimators=500, max_depth=7, learning_rate=0.08,
                                 random_state=self.random_state, objective='binary', verbose=-1),
            'rf': RandomForestClassifier(n_estimators=300, max_depth=10,
                                       random_state=self.random_state, n_jobs=-1)
        }

        # Train models
        for model in base_models.values():
            model.fit(X_scaled, y_encoded)

        # Get predictions on test set
        X_test_features = X_test[self.feature_names]
        X_test_scaled = self.scaler.transform(X_test_features)

        # Collect predictions
        test_predictions = []
        for model in base_models.values():
            pred_proba = model.predict_proba(X_test_scaled)[:, 1]
            test_predictions.append(pred_proba)

        # Average predictions
        avg_predictions = np.mean(test_predictions, axis=0)

        # Select high-confidence predictions
        high_conf_mask = (avg_predictions >= confidence_threshold) | (avg_predictions <= (1 - confidence_threshold))

        if np.sum(high_conf_mask) > 0:
            # Get pseudo-labels
            pseudo_labels = (avg_predictions[high_conf_mask] > 0.5).astype(int)
            pseudo_features = X_test_features.iloc[high_conf_mask]

            # Convert back to original labels
            pseudo_labels_original = self.label_encoder.inverse_transform(pseudo_labels)

            # Combine with original training data
            X_augmented = pd.concat([X_features, pseudo_features], ignore_index=True)
            y_augmented = pd.concat([y_train, pd.Series(pseudo_labels_original)], ignore_index=True)

            logger.info(f"Added {len(pseudo_features)} pseudo-labeled samples")
            logger.info(f"Augmented dataset size: {len(X_augmented)} (was {len(X_features)})")

            return X_augmented, y_augmented
        else:
            logger.warning("No high-confidence predictions found for pseudo-labeling")
            return X_features, y_train

    def train_optimized_models(self, X_train: pd.DataFrame, y_train: pd.Series) -> Dict:
        """
        PHASE 3: Train models with optimized hyperparameters.
        """
        logger.info("Training models with optimized hyperparameters...")

        X_features = X_train[self.feature_names]
        y_encoded = self.label_encoder.fit_transform(y_train)
        X_scaled = self.scaler.fit_transform(X_features)

        models = {}

        # Use optimized parameters if available
        if self.best_params:
            # XGBoost with optimized parameters
            xgb_params = {k.replace('xgb_', ''): v for k, v in self.best_params.items() if k.startswith('xgb_')}
            xgb_params.update({'random_state': self.random_state, 'eval_metric': 'logloss', 'device': 'cuda'})
            models['xgb_opt'] = XGBClassifier(**xgb_params)

            # LightGBM with optimized parameters
            lgb_params = {k.replace('lgb_', ''): v for k, v in self.best_params.items() if k.startswith('lgb_')}
            lgb_params.update({'random_state': self.random_state, 'objective': 'binary', 'metric': 'binary_logloss', 'verbose': -1})
            models['lgb_opt'] = LGBMClassifier(**lgb_params)
        else:
            # Default optimized parameters
            models['xgb_opt'] = XGBClassifier(n_estimators=600, max_depth=7, learning_rate=0.08,
                                            subsample=0.8, colsample_bytree=0.8,
                                            random_state=self.random_state, device='cuda')
            models['lgb_opt'] = LGBMClassifier(n_estimators=600, max_depth=7, learning_rate=0.08,
                                             subsample=0.8, colsample_bytree=0.8,
                                             random_state=self.random_state, objective='binary', verbose=-1)

        # Additional diverse models
        models['cat'] = CatBoostClassifier(iterations=600, depth=7, learning_rate=0.08,
                                         random_state=self.random_state, verbose=False, task_type='CPU')

        models['rf'] = RandomForestClassifier(n_estimators=400, max_depth=12, min_samples_split=4,
                                            random_state=self.random_state, n_jobs=-1)

        models['extra_trees'] = ExtraTreesClassifier(n_estimators=400, max_depth=12, min_samples_split=4,
                                                   random_state=self.random_state, n_jobs=-1)

        models['mlp'] = MLPClassifier(hidden_layer_sizes=(128, 64, 32), max_iter=500,
                                    random_state=self.random_state, early_stopping=True, alpha=0.001)

        models['svm'] = SVC(kernel='rbf', C=1.0, probability=True, random_state=self.random_state)

        models['nb'] = GaussianNB()

        # Train all models
        for name, model in models.items():
            logger.info(f"Training {name}...")
            model.fit(X_scaled, y_encoded)

        self.models = models
        logger.info(f"Trained {len(models)} optimized models successfully")
        return models
